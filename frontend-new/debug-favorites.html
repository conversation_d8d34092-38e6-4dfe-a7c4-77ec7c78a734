<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Favorites - Chrome Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .button:hover {
            background: #005a87;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>Chrome Extension Favorites Debug Tool</h1>
    
    <div class="section">
        <h3>Environment Check</h3>
        <button class="button" onclick="checkEnvironment()">Check Environment</button>
        <div id="env-log" class="log"></div>
    </div>

    <div class="section">
        <h3>Token Management</h3>
        <button class="button" onclick="checkToken()">Check Token</button>
        <button class="button" onclick="setTestToken()">Set Test Token</button>
        <button class="button" onclick="clearToken()">Clear Token</button>
        <div id="token-log" class="log"></div>
    </div>

    <div class="section">
        <h3>API Testing</h3>
        <button class="button" onclick="testGetFavorites()">Test Get Favorites</button>
        <button class="button" onclick="testAddFavorite()">Test Add Favorite</button>
        <button class="button" onclick="testCheckStatus()">Test Check Status</button>
        <div id="api-log" class="log"></div>
    </div>

    <div class="section">
        <h3>Background Script Communication</h3>
        <button class="button" onclick="testBackgroundComm()">Test Background Communication</button>
        <div id="bg-log" class="log"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function checkEnvironment() {
            const envLog = document.getElementById('env-log');
            envLog.innerHTML = '';
            
            log('env-log', 'Checking environment...', 'info');
            
            // Check if Chrome extension APIs are available
            if (typeof chrome !== 'undefined') {
                log('env-log', '✓ Chrome APIs available', 'success');
                
                if (chrome.runtime && chrome.runtime.id) {
                    log('env-log', `✓ Extension ID: ${chrome.runtime.id}`, 'success');
                } else {
                    log('env-log', '✗ No extension ID available', 'error');
                }
                
                if (chrome.runtime && chrome.runtime.sendMessage) {
                    log('env-log', '✓ sendMessage API available', 'success');
                } else {
                    log('env-log', '✗ sendMessage API not available', 'error');
                }
            } else {
                log('env-log', '✗ Chrome APIs not available', 'error');
            }
            
            // Check protocol
            log('env-log', `Protocol: ${window.location.protocol}`, 'info');
            log('env-log', `URL: ${window.location.href}`, 'info');
            
            // Check localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                log('env-log', '✓ localStorage available', 'success');
            } catch (e) {
                log('env-log', '✗ localStorage not available: ' + e.message, 'error');
            }
        }

        function checkToken() {
            const tokenLog = document.getElementById('token-log');
            tokenLog.innerHTML = '';
            
            log('token-log', 'Checking token...', 'info');
            
            try {
                const token = localStorage.getItem('token');
                if (token) {
                    log('token-log', `✓ Token found: ${token.substring(0, 20)}...`, 'success');
                    log('token-log', `Token length: ${token.length}`, 'info');
                    
                    if (token.startsWith('Token ')) {
                        log('token-log', '✓ Token has correct prefix', 'success');
                    } else if (token.startsWith('Bearer ')) {
                        log('token-log', '✓ Token has Bearer prefix', 'success');
                    } else {
                        log('token-log', '⚠ Token missing prefix', 'error');
                    }
                } else {
                    log('token-log', '✗ No token found', 'error');
                }
            } catch (e) {
                log('token-log', '✗ Error checking token: ' + e.message, 'error');
            }
        }

        function setTestToken() {
            const tokenLog = document.getElementById('token-log');
            
            // This is a test token - replace with actual token for testing
            const testToken = 'Token 1234567890abcdef1234567890abcdef12345678';
            
            try {
                localStorage.setItem('token', testToken);
                log('token-log', '✓ Test token set', 'success');
                checkToken();
            } catch (e) {
                log('token-log', '✗ Error setting token: ' + e.message, 'error');
            }
        }

        function clearToken() {
            const tokenLog = document.getElementById('token-log');
            
            try {
                localStorage.removeItem('token');
                log('token-log', '✓ Token cleared', 'success');
            } catch (e) {
                log('token-log', '✗ Error clearing token: ' + e.message, 'error');
            }
        }

        async function testGetFavorites() {
            const apiLog = document.getElementById('api-log');
            apiLog.innerHTML = '';
            
            log('api-log', 'Testing Get Favorites API...', 'info');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('api-log', '✗ No token available', 'error');
                    return;
                }
                
                // Test direct API call
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    log('api-log', 'Using Chrome extension proxy...', 'info');
                    
                    chrome.runtime.sendMessage({
                        type: 'PROXY_API_REQUEST',
                        data: {
                            url: '/crypto/favorites/',
                            method: 'GET',
                            headers: {
                                'Authorization': token,
                                'Content-Type': 'application/json'
                            }
                        }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            log('api-log', '✗ Chrome runtime error: ' + chrome.runtime.lastError.message, 'error');
                            return;
                        }
                        
                        if (response && response.success) {
                            log('api-log', '✓ API call successful', 'success');
                            log('api-log', 'Response: ' + JSON.stringify(response.data, null, 2), 'info');
                        } else {
                            log('api-log', '✗ API call failed', 'error');
                            log('api-log', 'Error: ' + (response?.error || 'Unknown error'), 'error');
                        }
                    });
                } else {
                    log('api-log', '✗ Chrome extension APIs not available', 'error');
                }
            } catch (e) {
                log('api-log', '✗ Error: ' + e.message, 'error');
            }
        }

        async function testAddFavorite() {
            const apiLog = document.getElementById('api-log');
            
            log('api-log', 'Testing Add Favorite API...', 'info');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('api-log', '✗ No token available', 'error');
                    return;
                }
                
                const testAsset = {
                    symbol: 'BTCUSDT',
                    market_type: 'crypto',
                    name: 'Bitcoin',
                    exchange: 'binance',
                    sector: null
                };
                
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    chrome.runtime.sendMessage({
                        type: 'PROXY_API_REQUEST',
                        data: {
                            url: '/crypto/favorites/',
                            method: 'POST',
                            headers: {
                                'Authorization': token,
                                'Content-Type': 'application/json'
                            },
                            body: testAsset
                        }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            log('api-log', '✗ Chrome runtime error: ' + chrome.runtime.lastError.message, 'error');
                            return;
                        }
                        
                        if (response && response.success) {
                            log('api-log', '✓ Add favorite successful', 'success');
                            log('api-log', 'Response: ' + JSON.stringify(response.data, null, 2), 'info');
                        } else {
                            log('api-log', '✗ Add favorite failed', 'error');
                            log('api-log', 'Error: ' + (response?.error || 'Unknown error'), 'error');
                        }
                    });
                } else {
                    log('api-log', '✗ Chrome extension APIs not available', 'error');
                }
            } catch (e) {
                log('api-log', '✗ Error: ' + e.message, 'error');
            }
        }

        async function testCheckStatus() {
            const apiLog = document.getElementById('api-log');
            
            log('api-log', 'Testing Check Favorite Status API...', 'info');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('api-log', '✗ No token available', 'error');
                    return;
                }
                
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                    chrome.runtime.sendMessage({
                        type: 'PROXY_API_REQUEST',
                        data: {
                            url: '/crypto/favorites/status/BTCUSDT/?market_type=crypto',
                            method: 'GET',
                            headers: {
                                'Authorization': token,
                                'Content-Type': 'application/json'
                            }
                        }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            log('api-log', '✗ Chrome runtime error: ' + chrome.runtime.lastError.message, 'error');
                            return;
                        }
                        
                        if (response && response.success) {
                            log('api-log', '✓ Check status successful', 'success');
                            log('api-log', 'Response: ' + JSON.stringify(response.data, null, 2), 'info');
                        } else {
                            log('api-log', '✗ Check status failed', 'error');
                            log('api-log', 'Error: ' + (response?.error || 'Unknown error'), 'error');
                        }
                    });
                } else {
                    log('api-log', '✗ Chrome extension APIs not available', 'error');
                }
            } catch (e) {
                log('api-log', '✗ Error: ' + e.message, 'error');
            }
        }

        function testBackgroundComm() {
            const bgLog = document.getElementById('bg-log');
            bgLog.innerHTML = '';
            
            log('bg-log', 'Testing background script communication...', 'info');
            
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                // Test basic communication
                chrome.runtime.sendMessage({
                    type: 'GET_CURRENT_SYMBOL'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('bg-log', '✗ Communication error: ' + chrome.runtime.lastError.message, 'error');
                        return;
                    }
                    
                    if (response) {
                        log('bg-log', '✓ Background script responded', 'success');
                        log('bg-log', 'Current symbol: ' + (response.symbol || 'none'), 'info');
                    } else {
                        log('bg-log', '✗ No response from background script', 'error');
                    }
                });
            } else {
                log('bg-log', '✗ Chrome extension APIs not available', 'error');
            }
        }

        // Auto-check environment on load
        window.addEventListener('load', () => {
            checkEnvironment();
        });
    </script>
</body>
</html>
